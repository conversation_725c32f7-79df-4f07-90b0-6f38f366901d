# MCP Knowledge Graph Construction System - Environment Configuration

# Required: OpenAI-compatible API Configuration
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.siliconflow.cn/v1
OPENAI_MODEL=Qwen/QwQ-32B

# Alternative API Providers (uncomment and modify as needed):
# OpenAI: OPENAI_BASE_URL=https://api.openai.com/v1
# DeepSeek: OPENAI_BASE_URL=https://api.deepseek.com
# Custom endpoint: OPENAI_BASE_URL=your_custom_endpoint

# Optional: System Configuration
# QUALITY_THRESHOLD=0.5          # Quality threshold for knowledge enhancement
# MAX_ENTITIES=50                # Maximum entities per knowledge graph
# VISUALIZATION_WIDTH=1200       # HTML visualization width in pixels
# VISUALIZATION_HEIGHT=800       # HTML visualization height in pixels

# Instructions:
# 1. Copy this file to .env
# 2. Replace 'your_api_key_here' with your actual API key
# 3. Adjust OPENAI_BASE_URL and OPENAI_MODEL for your preferred provider
# 4. Optionally configure the system parameters below
